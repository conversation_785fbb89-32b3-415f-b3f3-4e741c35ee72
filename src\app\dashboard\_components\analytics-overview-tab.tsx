"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { motion } from "framer-motion"
import { FocusTimeChart } from "@/components/stats/FocusTimeChart"
import { CompletionRateChart } from "@/components/stats/CompletionRateChart"
import { WeeklyComparison } from "@/components/stats/WeeklyComparison"
import { FocusDistribution } from "@/components/stats/FocusDistribution"
import { DailyHeatmap } from "@/components/stats/DailyHeatmap"
import { StreakCalendar } from "@/components/stats/StreakCalendar"
import { HorizontalSessionTimeline } from "@/components/stats/HorizontalSessionTimeline"
import { TrendingUp, Bar<PERSON><PERSON>, PieChart, Clock, Calendar, Target, Activity, Zap, Flame, Timer } from "lucide-react"
import { formatMinutesReadable, formatDurationReadable } from "@/lib/utils"

interface TodaySession {
  title?: string
  timeRange: string
  duration: number
  completed: boolean
}

interface Session {
  id: string
  title: string
  type: "focus" | "shortBreak" | "longBreak"
  startTime: string
  endTime: string
  completed: boolean
}

interface AnalyticsOverviewTabProps {
  transformedData: {
    dailyFocusTime: Array<{ date: string; minutes: number }>
    completionRate: { completed: number; interrupted: number }
    weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>
    hourlyDistribution: Array<{ hour: string; value: number }>
    dailyHeatmap: Array<{ day: string; hour: number; value: number }>
    todaySessions: TodaySession[]
    todayQuality: number
    recommendation: string
    streakData: Array<{ date: string; count: number }>
    daysActiveThisMonth: number
    longestStreak: number
    consistencyScore: number
    dailyCalendarSessions: Session[]
  }
  safeStatsData: {
    focusSessions: number
    shortBreakSessions: number
    longBreakSessions: number
    totalSessions: number
    completedSessions: number
    focusDuration: number
    shortBreakDuration: number
    longBreakDuration: number
  }
  timelineSessionData?: Session[]
  selectedTimelineDate?: string
  onTimelineDateChange?: (date: string) => void
}

export function AnalyticsOverviewTab({
  transformedData,
  safeStatsData,
  timelineSessionData,
  selectedTimelineDate,
  onTimelineDateChange
}: AnalyticsOverviewTabProps) {
  function formatTime(timeStr: string) {
    // Handles both 'HH:mm:ss' and ISO strings
    const date = timeStr.length > 8 ? new Date(timeStr) : new Date(`1970-01-01T${timeStr}`);
    return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
  }

  function formatTimeRange(timeRange: string) {
    // Expects 'start - end' (e.g., '14:00:00 - 14:25:00' or ISO)
    const [start, end] = timeRange.split(' - ');
    return `${formatTime(start)} - ${formatTime(end)}`;
  }

  // Animation variants that respect user preferences
  const getAnimationProps = (delay: number = 0) => ({
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: {
      duration: 0.3,
      delay,
      ease: "easeInOut"
    }
  });

  const getVerticalAnimationProps = (delay: number = 0) => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: {
      duration: 0.3,
      delay,
      ease: "easeInOut"
    }
  });

  return (
    <div className="space-y-6">
      {/* Core Metrics Section - Row 1: Focus Time Trend & Weekly Comparison */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-chart-3/[0.02] to-chart-3/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-orange-500/10 border border-orange-500/20 hover:bg-orange-500/15 transition-colors duration-200">
                <TrendingUp className="h-4 w-4 text-orange-500" aria-hidden="true" />
              </div>
              <span>Focus Time Trend</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Track your daily focus patterns and productivity trends over the past 30 days
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Focus time trend analysis">
            <FocusTimeChart data={transformedData.dailyFocusTime} />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-2/[0.02] to-chart-2/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-2/10 border border-chart-2/20 hover:bg-chart-2/15 transition-colors duration-200">
                <BarChart className="h-4 w-4 text-chart-2" aria-hidden="true" />
              </div>
              <span>Weekly Comparison</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Compare your focus time distribution across weeks
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Weekly focus time comparison across days">
            <WeeklyComparison data={transformedData.weeklyComparison} />
          </CardContent>
        </Card>
      </div>

      {/* Core Metrics Section - Row 2: Completion Rate & Focus Distribution */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-chart-3/[0.02] to-chart-3/[0.05] bg-card border-border shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-3/10 border border-border">
                <PieChart className="h-4 w-4 text-chart-3" />
              </div>
              Completion Rate
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Completed vs. interrupted sessions over the last 30 days (a session is <strong>completed</strong> when the focus timer finishes without manual interruption)</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <CompletionRateChart data={transformedData.completionRate} periodLabel="Last 30 days" />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-4/[0.02] to-chart-4/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-4/10 border border-chart-4/20 hover:bg-chart-4/15 transition-colors duration-200">
                <Clock className="h-4 w-4 text-chart-4" aria-hidden="true" />
              </div>
              <span>Focus Distribution</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              When you focus most during the day
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Hourly focus time distribution throughout the day">
            <FocusDistribution data={transformedData.hourlyDistribution} />
          </CardContent>
        </Card>
      </div>

      {/* Daily Patterns Section */}
      <div className="grid gap-4 lg:grid-cols-3">
        <Card className="bg-gradient-to-br from-chart-5/[0.02] to-chart-5/[0.05] bg-card border-border shadow-lg lg:col-span-2 overflow-hidden">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-5/10 border border-border">
                <Activity className="h-4 w-4 text-chart-5" />
              </div>
              Daily Focus Heatmap
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Your focus intensity throughout the day</CardDescription>
          </CardHeader>
          <CardContent className="pt-0 px-3 sm:px-6">
            <DailyHeatmap data={transformedData.dailyHeatmap} />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-primary/[0.02] to-primary/[0.05] bg-card border-border shadow-lg overflow-hidden">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-primary/10 border border-border">
                <Target className="h-4 w-4 text-primary" />
              </div>
              Today&apos;s Sessions
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Breakdown of today&apos;s focus sessions</CardDescription>
          </CardHeader>
          <CardContent className="h-[280px] overflow-auto pt-0 px-3 sm:px-6">
            <div className="space-y-3">
              {transformedData.todaySessions.map((session: TodaySession, index: number) => (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3 rounded-lg bg-accent/30 p-3 transition-colors hover:bg-accent/50"
                >
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-sm text-foreground truncate">{session.title || `Session ${index + 1}`}</p>
                    <p className="text-xs text-muted-foreground truncate">{formatTimeRange(session.timeRange)}</p>
                  </div>
                  <div className="flex-shrink-0 text-left sm:text-right">
                    <p className="text-sm font-bold text-primary" aria-label={`Session duration: ${formatMinutesReadable(session.duration)}`}>
                      {formatMinutesReadable(session.duration)}
                    </p>
                    {session.completed ? (
                      <span className="text-xs text-chart-2 font-medium">Completed</span>
                    ) : (
                      <span className="text-xs text-chart-4 font-medium">Interrupted</span>
                    )}
                  </div>
                </div>
              ))}

              {transformedData.todaySessions.length === 0 && (
                <p className="text-center text-muted-foreground py-8 text-sm">No sessions recorded today</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calendar & Insights Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-gradient-to-br from-chart-2/[0.02] to-chart-2/[0.05] bg-card border-border shadow-lg md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-2/10 border border-border">
                <Calendar className="h-4 w-4 text-chart-2" />
              </div>
              Focus Calendar
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Your focus activity over the past 30 days</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <StreakCalendar
              data={transformedData.streakData}
              dailyFocusTime={transformedData.dailyFocusTime}
            />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-1/[0.02] to-chart-1/[0.05] bg-card border-border shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-1/10 border border-border">
                <Zap className="h-4 w-4 text-chart-1" />
              </div>
              Focus Insights
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Patterns and recommendations</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Focus Quality</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-chart-2"
                      style={{ width: `${transformedData.todayQuality}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium text-foreground">{transformedData.todayQuality}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.todayQuality > 80
                    ? "Excellent focus! You completed most sessions without interruptions."
                    : transformedData.todayQuality > 50
                      ? "Good focus, with some interruptions."
                      : "You had several interruptions. Consider adjusting your environment."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Consistency Score</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-chart-4"
                      style={{ width: `${transformedData.consistencyScore}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium text-foreground">{transformedData.consistencyScore}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.consistencyScore > 80
                    ? "Excellent consistency! You're building a strong focus habit."
                    : transformedData.consistencyScore > 50
                      ? "Good consistency. Try to reduce gaps between active days."
                      : "Consider setting a regular schedule for better consistency."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Recommendation</h4>
                <p className="text-xs text-muted-foreground">{transformedData.recommendation}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Session Insights Section - Combined Streak Analysis and Duration */}
      <div className="grid gap-6">
        <Card className="bg-gradient-to-br from-primary/[0.02] to-chart-1/[0.04] bg-card border-border shadow-lg transition-all duration-300 hover:shadow-xl overflow-hidden">
          <CardHeader className="pb-4 border-b border-border/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-primary/10 to-chart-1/10 border border-primary/20">
                  <Activity className="h-5 w-5 text-primary" aria-hidden="true" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold text-foreground">Session Insights</CardTitle>
                  <CardDescription className="text-sm text-muted-foreground mt-1">
                    Your focus patterns, consistency, and productivity metrics
                  </CardDescription>
                </div>
              </div>
              <div className="hidden sm:flex items-center space-x-2 text-xs text-muted-foreground bg-accent/30 px-3 py-1.5 rounded-full">
                <div className="w-2 h-2 rounded-full bg-chart-1 animate-pulse"></div>
                <span>Live Data</span>
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-6">
            {/* Hero Metrics Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <motion.div
                className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-chart-1/5 to-chart-1/10 border border-chart-1/20 p-6 hover:shadow-lg transition-all duration-300"
                {...getAnimationProps(0.1)}
                role="group"
                aria-label="Focus streak overview"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="p-2 rounded-lg bg-chart-1/10">
                    <Flame className="h-5 w-5 text-chart-1" aria-hidden="true" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-chart-1" aria-label={`${transformedData.longestStreak} day streak`}>
                      {transformedData.longestStreak}
                    </div>
                    <div className="text-xs text-muted-foreground">day streak</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-foreground">Focus Streak</h3>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {transformedData.longestStreak > 7
                      ? "Outstanding consistency! Keep the momentum going."
                      : transformedData.longestStreak > 3
                        ? "Good progress! Building a solid habit."
                        : "Start building your focus streak today."}
                  </p>
                </div>
                <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-chart-1/5 rounded-full blur-xl"></div>
              </motion.div>

              <motion.div
                className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 p-6 hover:shadow-lg transition-all duration-300"
                {...getAnimationProps(0.2)}
                role="group"
                aria-label="Total focus time overview"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <Clock className="h-5 w-5 text-primary" aria-hidden="true" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary" aria-label={`Total focus time: ${formatDurationReadable(safeStatsData.focusDuration)}`}>
                      {formatDurationReadable(safeStatsData.focusDuration)}
                    </div>
                    <div className="text-xs text-muted-foreground">total focus</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-foreground">Focus Time</h3>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    Across {safeStatsData.focusSessions} sessions with {formatMinutesReadable(safeStatsData.focusSessions > 0 ? Math.round(safeStatsData.focusDuration / safeStatsData.focusSessions / 60) : 0)} average
                  </p>
                </div>
                <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-primary/5 rounded-full blur-xl"></div>
              </motion.div>

              <motion.div
                className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-chart-2/5 to-chart-2/10 border border-chart-2/20 p-6 hover:shadow-lg transition-all duration-300"
                {...getAnimationProps(0.3)}
                role="group"
                aria-label="Completion rate overview"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="p-2 rounded-lg bg-chart-2/10">
                    <Target className="h-5 w-5 text-chart-2" aria-hidden="true" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-chart-2" aria-label={`${Math.round((safeStatsData.completedSessions / Math.max(safeStatsData.focusSessions, 1)) * 100)}% completion rate`}>
                      {Math.round((safeStatsData.completedSessions / Math.max(safeStatsData.focusSessions, 1)) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">completion</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-foreground">Success Rate</h3>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {safeStatsData.completedSessions} of {safeStatsData.focusSessions} sessions completed
                  </p>
                </div>
                <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-chart-2/5 rounded-full blur-xl"></div>
              </motion.div>
            </div>

            {/* Detailed Analytics Grid */}
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Consistency Analytics */}
              <motion.div
                className="space-y-4"
                {...getVerticalAnimationProps(0.4)}
              >
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-1 h-6 bg-gradient-to-b from-chart-4 to-chart-4/50 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-foreground">Consistency Patterns</h3>
                </div>

                <div className="space-y-3">
                  <div className="rounded-xl bg-gradient-to-r from-chart-4/5 to-chart-4/10 border border-chart-4/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-foreground">Monthly Activity</span>
                      <span className="text-lg font-bold text-chart-4" aria-label={`${transformedData.daysActiveThisMonth} active days this month`}>
                        {transformedData.daysActiveThisMonth} days
                      </span>
                    </div>
                    <div className="w-full bg-muted/50 rounded-full h-2 mb-2">
                      <div
                        className="bg-gradient-to-r from-chart-4 to-chart-4/80 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min((transformedData.daysActiveThisMonth / 30) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {transformedData.daysActiveThisMonth > 20 ? "Excellent consistency!" : transformedData.daysActiveThisMonth > 10 ? "Good progress" : "Room for improvement"}
                    </p>
                  </div>

                  <div className="rounded-xl bg-gradient-to-r from-chart-2/5 to-chart-2/10 border border-chart-2/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-foreground">Peak Performance</span>
                      <span className="text-lg font-bold text-chart-2" aria-label={`${transformedData.streakData.length > 0 ? Math.max(...transformedData.streakData.map(d => d.count)) : 0} peak daily sessions`}>
                        {transformedData.streakData.length > 0 ? Math.max(...transformedData.streakData.map(d => d.count)) : 0} sessions
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Your best single-day session count
                    </p>
                  </div>

                  <div className="rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-foreground">Consistency Score</span>
                      <span className="text-lg font-bold text-primary" aria-label={`${transformedData.consistencyScore}% consistency score`}>
                        {transformedData.consistencyScore}%
                      </span>
                    </div>
                    <div className="w-full bg-muted/50 rounded-full h-2 mb-2">
                      <div
                        className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${transformedData.consistencyScore}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {transformedData.consistencyScore > 80 ? "Outstanding habit formation" : transformedData.consistencyScore > 60 ? "Building good habits" : "Focus on regular practice"}
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Time Analytics */}
              <motion.div
                className="space-y-4"
                {...getVerticalAnimationProps(0.5)}
              >
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-1 h-6 bg-gradient-to-b from-chart-1 to-chart-1/50 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-foreground">Time Distribution</h3>
                </div>

                <div className="space-y-3">
                  <div className="rounded-xl bg-gradient-to-r from-chart-1/5 to-chart-1/10 border border-chart-1/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-chart-1" aria-hidden="true" />
                        <span className="text-sm font-medium text-foreground">Focus Sessions</span>
                      </div>
                      <span className="text-lg font-bold text-chart-1" aria-label={`${safeStatsData.focusSessions} total focus sessions`}>
                        {safeStatsData.focusSessions}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="text-center p-2 rounded-lg bg-chart-1/10">
                        <div className="font-semibold text-chart-1">{safeStatsData.completedSessions}</div>
                        <div className="text-muted-foreground">Completed</div>
                      </div>
                      <div className="text-center p-2 rounded-lg bg-muted/30">
                        <div className="font-semibold text-muted-foreground">{safeStatsData.focusSessions - safeStatsData.completedSessions}</div>
                        <div className="text-muted-foreground">Interrupted</div>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl bg-gradient-to-r from-chart-2/5 to-chart-2/10 border border-chart-2/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Timer className="h-4 w-4 text-chart-2" aria-hidden="true" />
                        <span className="text-sm font-medium text-foreground">Break Time</span>
                      </div>
                      <span className="text-lg font-bold text-chart-2" aria-label={`Total break time: ${formatDurationReadable(safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)}`}>
                        {formatDurationReadable(safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="text-center p-2 rounded-lg bg-chart-2/10">
                        <div className="font-semibold text-chart-2" aria-label={`Short breaks: ${formatDurationReadable(safeStatsData.shortBreakDuration)}`}>
                          {formatDurationReadable(safeStatsData.shortBreakDuration)}
                        </div>
                        <div className="text-muted-foreground">Short Breaks</div>
                      </div>
                      <div className="text-center p-2 rounded-lg bg-chart-2/15">
                        <div className="font-semibold text-chart-2" aria-label={`Long breaks: ${formatDurationReadable(safeStatsData.longBreakDuration)}`}>
                          {formatDurationReadable(safeStatsData.longBreakDuration)}
                        </div>
                        <div className="text-muted-foreground">Long Breaks</div>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl bg-gradient-to-r from-chart-3/5 to-chart-3/10 border border-chart-3/20 p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Activity className="h-4 w-4 text-chart-3" aria-hidden="true" />
                        <span className="text-sm font-medium text-foreground">Session Balance</span>
                      </div>
                      <span className="text-lg font-bold text-chart-3">
                        {safeStatsData.focusDuration > 0 ? Math.round((safeStatsData.focusDuration / (safeStatsData.focusDuration + safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)) * 100) : 0}%
                      </span>
                    </div>
                    <div className="w-full bg-muted/50 rounded-full h-2 mb-2">
                      <div
                        className="bg-gradient-to-r from-chart-3 to-chart-3/80 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${safeStatsData.focusDuration > 0 ? Math.round((safeStatsData.focusDuration / (safeStatsData.focusDuration + safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)) * 100) : 0}%`
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Focus vs break time ratio
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Session Timeline Section */}
      <div className="mt-6 overflow-hidden">
        <HorizontalSessionTimeline
          sessions={timelineSessionData || transformedData.dailyCalendarSessions}
          date={selectedTimelineDate || new Date().toISOString().split("T")[0]}
          onDateChange={onTimelineDateChange}
        />
      </div>
    </div>
  )
}
